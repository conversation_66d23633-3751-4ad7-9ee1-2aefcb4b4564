# Fleet Management Services Schema - MVP Implementation

## Overview

This document outlines the streamlined database schema implementation for the fleet management services module, focusing on essential functionality for a Minimum Viable Product (MVP) approach.

## Migration Files Created/Updated

### 1. Service Types Migration

**File:** `database/migrations/2025_06_27_125001_create_service_types_table.php`

-   **Purpose:** Define different types of services (Oil Change, Brake Service, etc.)
-   **Key Features:**
    -   Service categorization (preventive, corrective, inspection)
    -   Default intervals and estimated costs
    -   Required skills and certifications
    -   Tool requirements and safety notes

### 2. Service Providers Migration

**File:** `database/migrations/2025_06_27_125002_create_service_providers_table.php`

-   **Purpose:** Manage external service providers and vendors
-   **Key Features:**
    -   Complete contact information
    -   Service specialties and ratings
    -   Certification tracking
    -   Operating hours and emergency service flags

### 3. Simplified Services Migration (MVP)

**File:** `database/migrations/2025_06_27_130008_create_services_table.php` (Simplified)

-   **Purpose:** Essential service record management
-   **Core Features:**
    -   Service identification with unique service numbers
    -   Basic scheduling with datetime precision
    -   Essential odometer tracking
    -   Simplified financial tracking (estimated cost, total cost, paid amount)
    -   Core status management
    -   Vehicle and technician assignment
    -   Service type and provider relationships

### 4. Service Parts Migration

**File:** `database/migrations/2025_06_27_130009_create_service_parts_table.php`

-   **Purpose:** Track parts used in services
-   **Key Features:**
    -   Part identification and supplier tracking
    -   Cost breakdown with markup calculations
    -   Warranty management
    -   Core part handling
    -   Installation notes

## Model Classes Created/Updated

### 1. Service Model (`app/Models/Service.php`)

-   **Enhanced with:**
    -   Comprehensive fillable fields
    -   Proper relationship definitions
    -   JSON field casting
    -   Soft deletes support
    -   Calculated attributes (due_amount, is_overdue, actual_duration)

### 2. ServiceType Model (`app/Models/ServiceType.php`)

-   **Features:**
    -   Service type categorization
    -   Skill and certification requirements
    -   Active/inactive status management
    -   Ordering and scoping methods

### 3. ServiceProvider Model (`app/Models/ServiceProvider.php`)

-   **Features:**
    -   Complete vendor information management
    -   Rating and certification tracking
    -   Specialty and emergency service flags
    -   Address formatting utilities

### 4. ServicePart Model (`app/Models/ServicePart.php`)

-   **Features:**
    -   Part cost calculations
    -   Warranty validation
    -   Core part management
    -   Condition tracking

### 5. Updated Existing Models

-   **Vehicle Model:** Added services relationship
-   **User Model:** Added service assignment relationships

## Database Schema Enhancements (MVP Focus)

### Key Improvements Made:

1. **Data Integrity:**

    - Basic check constraints for financial fields (cost >= 0, paid_amount <= total_cost)
    - Date validation constraints (end_datetime >= start_datetime)
    - Simplified validation for production readiness

2. **Performance Optimization:**

    - 8 essential indexes for common query patterns
    - Strategic indexing for vehicle-service relationships
    - Status and priority-based queries
    - Foreign key indexes for relationships

3. **Core Service Management:**

    - Service lifecycle management (scheduled → completed)
    - Basic financial tracking (estimated, total, paid amounts)
    - Essential odometer tracking (start/end readings)
    - Status management with 5 core states

4. **Essential Relationships:**

    - Vehicle assignment and tracking
    - Technician assignment
    - Service type categorization
    - Service provider management

5. **Streamlined Features:**

    - Unique service number generation
    - Basic scheduling with datetime precision
    - Work order and invoice number tracking
    - Simple notes field for documentation

## Removed Advanced Features (For Future Implementation):

-   **Digital Assets:** File attachments, before/after photos, inspection checklists
-   **GPS & Location:** Latitude/longitude coordinates, detailed location tracking
-   **Recurring Services:** Parent-child relationships, automatic scheduling patterns
-   **Advanced Compliance:** Warranty tracking, recall management, certification requirements
-   **Quality Management:** Rating systems, detailed feedback, quality notes
-   **Advanced Financial:** Labor/parts/external cost breakdown, tax/discount tracking
-   **Soft Deletes:** Removed for simplicity, can be added later
-   **Complex Status:** Reduced from 10 to 5 essential status options
-   **Advanced Timing:** Removed next service due dates, service intervals

## Database Constraints Added (Simplified)

```sql
-- Basic financial constraints
CHECK (total_cost >= 0)
CHECK (paid_amount >= 0)
CHECK (paid_amount <= total_cost)

-- Date constraints
CHECK (scheduled_end_datetime >= scheduled_start_datetime)
```

## Performance Indexes (Essential)

### Core Indexes:

-   `['vehicle_id', 'status']` - Vehicle service history queries
-   `['service_type_id', 'service_category']` - Service type filtering
-   `['scheduled_start_datetime', 'status']` - Scheduling queries
-   `['assigned_technician_id', 'status']` - Technician workload
-   `['service_provider_id']` - Provider service history
-   `['created_by']` - User activity tracking
-   `['payment_status']` - Financial reporting
-   `['status', 'priority']` - Priority-based filtering

## Seeder Data

### ServiceTypeSeeder

**File:** `database/seeders/ServiceTypeSeeder.php`

-   Pre-populated with 8 common service types
-   Includes preventive, corrective, and inspection categories
-   Realistic cost estimates and duration
-   Certification requirements where applicable

## Migration Order

The migrations are ordered to ensure proper foreign key relationships:

1. `2025_06_27_125001_create_service_types_table.php`
2. `2025_06_27_125002_create_service_providers_table.php`
3. `2025_06_27_130008_create_services_table.php` (updated)
4. `2025_06_27_130009_create_service_parts_table.php`

## Next Steps

1. **Run Migrations:**

    ```bash
    php artisan migrate
    ```

2. **Seed Service Types:**

    ```bash
    php artisan db:seed --class=ServiceTypeSeeder
    ```

3. **Create Filament Resources:**

    - ServiceResource for CRUD operations
    - ServiceTypeResource for service type management
    - ServiceProviderResource for vendor management

4. **Implement Business Logic:**

    - Service scheduling algorithms
    - Automatic service number generation
    - Basic cost calculation methods
    - Status workflow management

5. **Add Validation Rules:**

    - Form request classes
    - Business rule validation
    - Data consistency checks

6. **Future Enhancements (Phase 2):**
    - Add recurring service support
    - Implement digital asset management
    - Add GPS location tracking
    - Expand financial tracking
    - Add quality management features

This streamlined MVP schema provides a solid foundation for fleet management services with essential functionality, while maintaining the flexibility to add advanced features in future iterations.
