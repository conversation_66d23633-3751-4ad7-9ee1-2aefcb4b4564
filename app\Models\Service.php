<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Service extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'service_number',
        'name',
        'description',
        'service_type_id',
        'service_category',
        'priority',
        'vehicle_id',
        'assigned_technician_id',
        'service_provider_id',
        'created_by',
        'updated_by',
        'scheduled_start_datetime',
        'scheduled_end_datetime',
        'actual_start_datetime',
        'actual_end_datetime',
        'next_service_due_date',
        'estimated_duration_hours',
        'actual_duration_hours',
        'odometer_reading_start',
        'odometer_reading_end',
        'next_service_odometer',
        'service_interval_km',
        'service_interval_months',
        'estimated_cost',
        'labor_cost',
        'parts_cost',
        'external_cost',
        'total_cost',
        'paid_amount',
        'currency',
        'tax_amount',
        'discount_amount',
        'status',
        'payment_status',
        'approval_status',
        'is_warranty_service',
        'is_recall_service',
        'is_compliance_required',
        'compliance_due_date',
        'work_order_number',
        'invoice_number',
        'required_certifications',
        'safety_requirements',
        'quality_rating',
        'quality_notes',
        'technician_notes',
        'customer_feedback',
        'internal_notes',
        'is_recurring',
        'parent_service_id',
        'recurrence_type',
        'recurrence_interval',
        'recurrence_end_date',
        'service_location',
        'location_address',
        'location_latitude',
        'location_longitude',
        'attachments',
        'before_photos',
        'after_photos',
        'inspection_checklist',
    ];

    protected $casts = [
        'scheduled_start_datetime' => 'datetime',
        'scheduled_end_datetime' => 'datetime',
        'actual_start_datetime' => 'datetime',
        'actual_end_datetime' => 'datetime',
        'next_service_due_date' => 'date',
        'compliance_due_date' => 'date',
        'recurrence_end_date' => 'date',
        'estimated_cost' => 'decimal:2',
        'labor_cost' => 'decimal:2',
        'parts_cost' => 'decimal:2',
        'external_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'location_latitude' => 'decimal:8',
        'location_longitude' => 'decimal:8',
        'is_warranty_service' => 'boolean',
        'is_recall_service' => 'boolean',
        'is_compliance_required' => 'boolean',
        'is_recurring' => 'boolean',
        'required_certifications' => 'array',
        'safety_requirements' => 'array',
        'attachments' => 'array',
        'before_photos' => 'array',
        'after_photos' => 'array',
        'inspection_checklist' => 'array',
    ];

    // Relationships
    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class);
    }

    public function serviceType(): BelongsTo
    {
        return $this->belongsTo(ServiceType::class);
    }

    public function assignedTechnician(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_technician_id');
    }

    public function serviceProvider(): BelongsTo
    {
        return $this->belongsTo(ServiceProvider::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function parentService(): BelongsTo
    {
        return $this->belongsTo(Service::class, 'parent_service_id');
    }

    public function childServices(): HasMany
    {
        return $this->hasMany(Service::class, 'parent_service_id');
    }

    public function serviceParts(): HasMany
    {
        return $this->hasMany(ServicePart::class);
    }

    // Accessors & Mutators
    protected function dueAmount(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->total_cost - $this->paid_amount,
        );
    }

    protected function isOverdue(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->scheduled_start_datetime < now() && !in_array($this->status, ['completed', 'cancelled']),
        );
    }

    protected function actualDuration(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->actual_start_datetime && $this->actual_end_datetime
                ? $this->actual_start_datetime->diffInHours($this->actual_end_datetime)
                : null,
        );
    }
}
