<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Service extends Model
{

    protected $guarded = [];

    protected $casts = [
        'scheduled_start_datetime' => 'datetime',
        'scheduled_end_datetime' => 'datetime',
        'actual_start_datetime' => 'datetime',
        'actual_end_datetime' => 'datetime',
        'estimated_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'paid_amount' => 'decimal:2',
    ];

    // Relationships
    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class);
    }

    public function serviceType(): BelongsTo
    {
        return $this->belongsTo(ServiceType::class);
    }

    public function serviceProvider(): BelongsTo
    {
        return $this->belongsTo(ServiceProvider::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function serviceParts(): HasMany
    {
        return $this->hasMany(ServicePart::class);
    }

    // Accessors & Mutators
    protected function dueAmount(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->total_cost - $this->paid_amount,
        );
    }

    protected function isOverdue(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->scheduled_start_datetime < now() && !in_array($this->status, ['completed', 'cancelled']),
        );
    }

    protected function actualDuration(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->actual_start_datetime && $this->actual_end_datetime
                ? $this->actual_start_datetime->diffInHours($this->actual_end_datetime)
                : null,
        );
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByVehicle($query, $vehicleId)
    {
        return $query->where('vehicle_id', $vehicleId);
    }

    public function scopeScheduledBetween($query, $startDate, $endDate)
    {
        return $query->whereBetween('scheduled_start_datetime', [$startDate, $endDate]);
    }

    public function scopeOverdue($query)
    {
        return $query->where('scheduled_start_datetime', '<', now())
            ->whereNotIn('status', ['completed', 'cancelled']);
    }
}
