<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ServiceType extends Model
{
    protected $guarded = [];

    protected $casts = [
        'estimated_cost' => 'decimal:2',
        'requires_certification' => 'boolean',
        'is_active' => 'boolean',
        'required_skills' => 'array',
        'required_certifications' => 'array',
        'required_tools' => 'array',
    ];

    // Relationships
    public function services(): HasMany
    {
        return $this->hasMany(Service::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
