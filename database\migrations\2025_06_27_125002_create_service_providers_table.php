<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_providers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('company_registration')->nullable();
            $table->string('tax_id')->nullable();
            $table->string('contact_person')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('mobile')->nullable();
            $table->string('website')->nullable();
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('country')->default('US');
            $table->json('service_specialties')->nullable(); // ["brake", "engine", "electrical"]
            $table->decimal('hourly_rate', 8, 2)->nullable();
            $table->decimal('markup_percentage', 5, 2)->nullable(); // for parts
            $table->tinyInteger('rating')->nullable(); // 1-5
            $table->text('rating_notes')->nullable();
            $table->boolean('is_certified')->default(false);
            $table->json('certifications')->nullable(); // ["ASE", "ISO9001"]
            $table->date('certification_expiry')->nullable();
            $table->boolean('is_preferred_vendor')->default(false);
            $table->boolean('is_emergency_service')->default(false);
            $table->json('operating_hours')->nullable(); // {"monday": "08:00-17:00"}
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['is_active', 'is_preferred_vendor']);
            $table->index(['is_emergency_service', 'is_active']);
            $table->index(['rating', 'is_active']);
            $table->index(['email']);
            $table->index(['phone']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_providers');
    }
};
