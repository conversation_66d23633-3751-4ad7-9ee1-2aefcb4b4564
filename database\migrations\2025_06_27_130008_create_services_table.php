<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('services', function (Blueprint $table) {
            // Primary Key
            $table->id();

            // Service Identification & Classification
            $table->string('service_number')->unique(); // e.g., SRV-2025-001
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('service_type_id')->constrained()->cascadeOnDelete();
            $table->enum('service_category', [
                'preventive',
                'corrective',
                'emergency',
                'inspection',
                'warranty',
                'recall',
                'upgrade'
            ])->default('preventive');
            $table->enum('priority', ['low', 'medium', 'high', 'critical'])->default('medium');

            // Vehicle & Assignment Relationships
            $table->foreignId('vehicle_id')->constrained()->cascadeOnDelete();
            $table->foreignId('assigned_technician_id')->nullable()->constrained('users');
            $table->foreignId('service_provider_id')->nullable()->constrained();
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users');

            // Scheduling & Timing
            $table->datetime('scheduled_start_datetime');
            $table->datetime('scheduled_end_datetime');
            $table->datetime('actual_start_datetime')->nullable();
            $table->datetime('actual_end_datetime')->nullable();
            $table->date('next_service_due_date')->nullable();
            $table->integer('estimated_duration_hours')->nullable();
            $table->integer('actual_duration_hours')->nullable();

            // Odometer & Usage Tracking
            $table->unsignedInteger('odometer_reading_start')->nullable();
            $table->unsignedInteger('odometer_reading_end')->nullable();
            $table->unsignedInteger('next_service_odometer')->nullable();
            $table->unsignedInteger('service_interval_km')->nullable();
            $table->unsignedInteger('service_interval_months')->nullable();

            // Financial Information
            $table->decimal('estimated_cost', 12, 2)->nullable();
            $table->decimal('labor_cost', 12, 2)->default(0);
            $table->decimal('parts_cost', 12, 2)->default(0);
            $table->decimal('external_cost', 12, 2)->default(0); // outsourced services
            $table->decimal('total_cost', 12, 2)->default(0);
            $table->decimal('paid_amount', 12, 2)->default(0);
            $table->string('currency', 3)->default('USD');
            $table->decimal('tax_amount', 10, 2)->default(0);
            $table->decimal('discount_amount', 10, 2)->default(0);

            // Status & Workflow
            $table->enum('status', [
                'draft',
                'scheduled',
                'confirmed',
                'in_progress',
                'waiting_parts',
                'on_hold',
                'completed',
                'cancelled',
                'rejected',
                'rescheduled'
            ])->default('draft');
            $table->enum('payment_status', [
                'not_applicable',
                'pending',
                'partial',
                'paid',
                'overdue',
                'refunded'
            ])->default('not_applicable');
            $table->enum('approval_status', [
                'not_required',
                'pending',
                'approved',
                'rejected'
            ])->default('not_required');

            // Compliance & Documentation
            $table->boolean('is_warranty_service')->default(false);
            $table->boolean('is_recall_service')->default(false);
            $table->boolean('is_compliance_required')->default(false);
            $table->date('compliance_due_date')->nullable();
            $table->string('work_order_number')->nullable();
            $table->string('invoice_number')->nullable();
            $table->json('required_certifications')->nullable(); // ["ISO9001", "DOT"]
            $table->json('safety_requirements')->nullable();

            // Quality & Feedback
            $table->tinyInteger('quality_rating')->nullable(); // 1-5 scale
            $table->text('quality_notes')->nullable();
            $table->text('technician_notes')->nullable();
            $table->text('customer_feedback')->nullable();
            $table->text('internal_notes')->nullable();

            // Recurring Service Support
            $table->boolean('is_recurring')->default(false);
            $table->foreignId('parent_service_id')->nullable()->constrained('services');
            $table->enum('recurrence_type', [
                'none',
                'daily',
                'weekly',
                'monthly',
                'quarterly',
                'semi_annual',
                'annual',
                'mileage_based'
            ])->default('none');
            $table->integer('recurrence_interval')->nullable();
            $table->date('recurrence_end_date')->nullable();

            // Location & Environment
            $table->string('service_location')->nullable(); // garage, field, external
            $table->text('location_address')->nullable();
            $table->decimal('location_latitude', 10, 8)->nullable();
            $table->decimal('location_longitude', 11, 8)->nullable();

            // Digital Assets
            $table->json('attachments')->nullable(); // file paths
            $table->json('before_photos')->nullable();
            $table->json('after_photos')->nullable();
            $table->json('inspection_checklist')->nullable();

            // Soft Deletes & Timestamps
            $table->softDeletes();
            $table->timestamps();

            // Database Constraints
            $table->check('total_cost >= 0');
            $table->check('paid_amount >= 0');
            $table->check('paid_amount <= total_cost');
            $table->check('scheduled_end_datetime >= scheduled_start_datetime');
            $table->check('quality_rating IS NULL OR (quality_rating >= 1 AND quality_rating <= 5)');
            $table->check('odometer_reading_end IS NULL OR odometer_reading_start IS NULL OR odometer_reading_end >= odometer_reading_start');

            // Indexes for Performance
            $table->index(['vehicle_id', 'status']);
            $table->index(['service_type_id', 'service_category']);
            $table->index(['scheduled_start_datetime', 'status']);
            $table->index(['next_service_due_date']);
            $table->index(['assigned_technician_id', 'status']);
            $table->index(['service_provider_id']);
            $table->index(['created_by']);
            $table->index(['odometer_reading_start']);
            $table->index(['is_recurring', 'recurrence_type']);
            $table->index(['compliance_due_date']);
            $table->index(['payment_status', 'total_cost']);
            $table->index(['vehicle_id', 'scheduled_start_datetime']);
            $table->index(['status', 'priority', 'scheduled_start_datetime']);
            $table->index(['service_category', 'status']);
            $table->index(['assigned_technician_id', 'scheduled_start_datetime']);
            $table->index(['next_service_due_date', 'vehicle_id']);
            $table->index(['is_recurring', 'parent_service_id']);
            $table->index(['compliance_due_date', 'is_compliance_required']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('services');
    }
};
