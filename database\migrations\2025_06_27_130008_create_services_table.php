<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('services', function (Blueprint $table) {
            // Primary Key
            $table->id();

            // Service Identification & Classification
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('service_type_id')->constrained()->cascadeOnDelete();
            $table->enum('service_category', [
                'preventive',
                'corrective',
                'emergency',
                'inspection'
            ])->default('preventive');
            $table->enum('priority', ['low', 'medium', 'high', 'critical'])->default('medium');

            // Vehicle & Assignment Relationships
            $table->foreignId('vehicle_id')->constrained()->cascadeOnDelete();
            $table->string('assigned_technician')->nullable();
            $table->foreignId('service_provider_id')->nullable()->constrained();
            $table->foreignId('created_by')->constrained('users');

            // Scheduling & Timing
            $table->datetime('scheduled_start_datetime');
            $table->datetime('scheduled_end_datetime');
            $table->datetime('actual_start_datetime')->nullable();
            $table->datetime('actual_end_datetime')->nullable();
            // Odometer Tracking (Basic)
            $table->unsignedInteger('odometer_reading_start')->nullable();
            $table->unsignedInteger('odometer_reading_end')->nullable();

            // Financial Information (Simplified)
            $table->decimal('total_cost', 10, 2)->default(0);
            $table->decimal('paid_amount', 10, 2)->default(0);

            // Status Management
            $table->enum('status', [
                'scheduled',
                'in_progress',
                'completed',
                'cancelled',
                'on_hold'
            ])->default('scheduled');
            $table->enum('payment_status', [
                'pending',
                'partial',
                'paid',
                'not_applicable'
            ])->default('not_applicable');
            $table->text('notes')->nullable();

            // Timestamps
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('services');
    }
};
