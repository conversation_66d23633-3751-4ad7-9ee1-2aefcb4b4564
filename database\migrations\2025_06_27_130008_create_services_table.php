<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('services', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('description')->nullable();
            $table->decimal('total_amount', 10, 2);
            $table->decimal('paid_amount', 10, 2);
            $table->decimal('due_amount', 10, 2);
            $table->date('service_start_date');
            $table->date('service_end_date');
            $table->foreignId('vehicle_id')->constrained()->cascadeOnDelete();
            $table->enum('status', ['scheduled', 'in_progress', 'cancelled', 'on_hold', 'completed'])->default('scheduled');
            $table->enum('payment_status', ['paid', 'partial', 'due'])->default('due');
            $table->string('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('services');
    }
};
