<?php

namespace Database\Seeders;

use App\Models\ServiceType;
use Illuminate\Database\Seeder;

class ServiceTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $serviceTypes = [
            [
                'name' => 'Oil Change',
                'description' => 'Regular engine oil and filter replacement',
                'code' => 'OIL_CHANGE',
                'category' => 'preventive',
                'default_interval_km' => 5000,
                'default_interval_months' => 6,
                'estimated_cost' => 50.00,
                'estimated_duration_hours' => 1,
                'required_skills' => ['basic_maintenance'],
                'requires_certification' => false,
                'sort_order' => 1,
            ],
            [
                'name' => 'Brake Inspection',
                'description' => 'Complete brake system inspection and service',
                'code' => 'BRAKE_INSPECTION',
                'category' => 'preventive',
                'default_interval_km' => 20000,
                'default_interval_months' => 12,
                'estimated_cost' => 150.00,
                'estimated_duration_hours' => 2,
                'required_skills' => ['brake_systems'],
                'requires_certification' => true,
                'required_certifications' => ['ASE_A5'],
                'sort_order' => 2,
            ],
            [
                'name' => 'Tire Rotation',
                'description' => 'Rotate tires to ensure even wear',
                'code' => 'TIRE_ROTATION',
                'category' => 'preventive',
                'default_interval_km' => 10000,
                'default_interval_months' => 6,
                'estimated_cost' => 30.00,
                'estimated_duration_hours' => 1,
                'required_skills' => ['basic_maintenance'],
                'requires_certification' => false,
                'sort_order' => 3,
            ],
            [
                'name' => 'Engine Diagnostic',
                'description' => 'Computer diagnostic scan and analysis',
                'code' => 'ENGINE_DIAGNOSTIC',
                'category' => 'corrective',
                'estimated_cost' => 120.00,
                'estimated_duration_hours' => 2,
                'required_skills' => ['diagnostics', 'electrical'],
                'requires_certification' => true,
                'required_certifications' => ['ASE_A8'],
                'required_tools' => ['diagnostic_scanner', 'multimeter'],
                'sort_order' => 4,
            ],
            [
                'name' => 'Annual Safety Inspection',
                'description' => 'Mandatory annual vehicle safety inspection',
                'code' => 'ANNUAL_INSPECTION',
                'category' => 'inspection',
                'default_interval_months' => 12,
                'estimated_cost' => 75.00,
                'estimated_duration_hours' => 1,
                'required_skills' => ['inspection'],
                'requires_certification' => true,
                'required_certifications' => ['DOT_INSPECTOR'],
                'sort_order' => 5,
            ],
            [
                'name' => 'Transmission Service',
                'description' => 'Transmission fluid change and filter replacement',
                'code' => 'TRANSMISSION_SERVICE',
                'category' => 'preventive',
                'default_interval_km' => 60000,
                'default_interval_months' => 24,
                'estimated_cost' => 200.00,
                'estimated_duration_hours' => 3,
                'required_skills' => ['transmission'],
                'requires_certification' => true,
                'required_certifications' => ['ASE_A2'],
                'sort_order' => 6,
            ],
            [
                'name' => 'Emergency Repair',
                'description' => 'Emergency roadside or breakdown repair',
                'code' => 'EMERGENCY_REPAIR',
                'category' => 'corrective',
                'estimated_cost' => 300.00,
                'estimated_duration_hours' => 4,
                'required_skills' => ['general_repair', 'diagnostics'],
                'requires_certification' => false,
                'sort_order' => 7,
            ],
            [
                'name' => 'Air Conditioning Service',
                'description' => 'A/C system inspection, recharge, and repair',
                'code' => 'AC_SERVICE',
                'category' => 'preventive',
                'default_interval_months' => 12,
                'estimated_cost' => 180.00,
                'estimated_duration_hours' => 2,
                'required_skills' => ['hvac', 'refrigeration'],
                'requires_certification' => true,
                'required_certifications' => ['EPA_609'],
                'sort_order' => 8,
            ],
        ];

        foreach ($serviceTypes as $serviceType) {
            ServiceType::create($serviceType);
        }
    }
}
