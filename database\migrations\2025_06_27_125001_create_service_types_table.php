<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_types', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Oil Change, Brake Service, etc.
            $table->text('description')->nullable();
            $table->string('code')->unique(); // OIL_CHANGE, BRAKE_SVC
            $table->enum('category', ['preventive', 'corrective', 'inspection'])->default('preventive');
            $table->integer('default_interval_km')->nullable();
            $table->integer('default_interval_months')->nullable();
            $table->decimal('estimated_cost', 10, 2)->nullable();
            $table->integer('estimated_duration_hours')->nullable();
            $table->json('required_skills')->nullable(); // ["mechanical", "electrical", "hydraulic"]
            $table->boolean('requires_certification')->default(false);
            $table->json('required_certifications')->nullable(); // ["ASE", "DOT"]
            $table->json('required_tools')->nullable(); // ["lift", "diagnostic_scanner"]
            $table->text('safety_notes')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            // Indexes
            $table->index(['category', 'is_active']);
            $table->index(['is_active', 'sort_order']);
            $table->index(['code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_types');
    }
};
