<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ServiceProvider extends Model
{
    protected $fillable = [
        'name',
        'company_registration',
        'tax_id',
        'contact_person',
        'email',
        'phone',
        'mobile',
        'website',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'service_specialties',
        'hourly_rate',
        'markup_percentage',
        'rating',
        'rating_notes',
        'is_certified',
        'certifications',
        'certification_expiry',
        'is_preferred_vendor',
        'is_emergency_service',
        'operating_hours',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'hourly_rate' => 'decimal:2',
        'markup_percentage' => 'decimal:2',
        'certification_expiry' => 'date',
        'is_certified' => 'boolean',
        'is_preferred_vendor' => 'boolean',
        'is_emergency_service' => 'boolean',
        'is_active' => 'boolean',
        'service_specialties' => 'array',
        'certifications' => 'array',
        'operating_hours' => 'array',
    ];

    // Relationships
    public function services(): HasMany
    {
        return $this->hasMany(Service::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopePreferred($query)
    {
        return $query->where('is_preferred_vendor', true);
    }

    public function scopeEmergencyService($query)
    {
        return $query->where('is_emergency_service', true);
    }

    public function scopeByRating($query, $minRating = 3)
    {
        return $query->where('rating', '>=', $minRating);
    }

    // Accessors
    public function getFullAddressAttribute()
    {
        $parts = array_filter([
            $this->address,
            $this->city,
            $this->state,
            $this->postal_code,
            $this->country
        ]);
        
        return implode(', ', $parts);
    }
}
