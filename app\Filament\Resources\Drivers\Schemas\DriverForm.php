<?php

namespace App\Filament\Resources\Drivers\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class DriverForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required(),
                TextInput::make('email')
                    ->email()
                    ->required(),
                TextInput::make('phone')
                    ->tel()
                    ->required(),
                Select::make('gender')
                    ->options(['male' => 'Male', 'female' => 'Female'])
                    ->default('male')
                    ->required(),
                DatePicker::make('date_of_birth')
                    ->required(),
                DatePicker::make('joining_date')
                    ->required(),
                Textarea::make('address')
                    ->required(),
                TextInput::make('license_number')
                    ->required(),
                DatePicker::make('license_issue_date')
                    ->required(),
                DatePicker::make('license_expiry_date')
                    ->required(),
                FileUpload::make('license_document')
                    ->label('License Document (pdf, doc)')
                    ->directory('drivers-documents')
                    ->acceptedFileTypes(['application/pdf', 'application/msword'])
                    ->maxSize(1024)
                    ->default(null),
                TextInput::make('notes')
                    ->default(null),
            ]);
    }
}
