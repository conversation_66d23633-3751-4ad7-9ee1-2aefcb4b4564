<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_parts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_id')->constrained()->cascadeOnDelete();
            $table->string('part_number');
            $table->string('part_name');
            $table->text('description')->nullable();
            $table->string('manufacturer')->nullable();
            $table->string('supplier')->nullable();
            $table->integer('quantity_used');
            $table->decimal('unit_cost', 10, 2);
            $table->decimal('total_cost', 10, 2);
            $table->boolean('is_warranty_covered')->default(false);
            $table->date('warranty_start_date')->nullable();
            $table->date('warranty_expiry_date')->nullable();
            $table->integer('warranty_duration_months')->nullable();
            $table->text('warranty_terms')->nullable();
            $table->string('serial_number')->nullable();
            $table->string('batch_number')->nullable();
            $table->enum('part_condition', ['new', 'refurbished', 'used'])->default('new');
            $table->text('installation_notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_parts');
    }
};
