<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ServicePart extends Model
{
    protected $fillable = [
        'service_id',
        'part_number',
        'part_name',
        'description',
        'manufacturer',
        'supplier',
        'quantity_used',
        'unit_cost',
        'total_cost',
        'markup_percentage',
        'markup_amount',
        'currency',
        'is_warranty_covered',
        'warranty_start_date',
        'warranty_expiry_date',
        'warranty_duration_months',
        'warranty_terms',
        'serial_number',
        'batch_number',
        'part_condition',
        'is_core_part',
        'core_charge',
        'core_returned',
        'installation_notes',
    ];

    protected $casts = [
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'markup_percentage' => 'decimal:2',
        'markup_amount' => 'decimal:2',
        'core_charge' => 'decimal:2',
        'warranty_start_date' => 'date',
        'warranty_expiry_date' => 'date',
        'is_warranty_covered' => 'boolean',
        'is_core_part' => 'boolean',
        'core_returned' => 'boolean',
    ];

    // Relationships
    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    // Accessors
    public function getFinalCostAttribute()
    {
        return $this->total_cost + $this->markup_amount;
    }

    public function getIsWarrantyValidAttribute()
    {
        return $this->is_warranty_covered && 
               $this->warranty_expiry_date && 
               $this->warranty_expiry_date->isFuture();
    }

    // Scopes
    public function scopeWarrantyCovered($query)
    {
        return $query->where('is_warranty_covered', true);
    }

    public function scopeByCondition($query, $condition)
    {
        return $query->where('part_condition', $condition);
    }

    public function scopeCoreParts($query)
    {
        return $query->where('is_core_part', true);
    }
}
