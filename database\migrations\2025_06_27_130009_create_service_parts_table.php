<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_parts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_id')->constrained()->cascadeOnDelete();
            $table->string('part_number');
            $table->string('part_name');
            $table->text('description')->nullable();
            $table->string('manufacturer')->nullable();
            $table->string('supplier')->nullable();
            $table->integer('quantity_used');
            $table->decimal('unit_cost', 10, 2);
            $table->decimal('total_cost', 10, 2);
            $table->decimal('markup_percentage', 5, 2)->default(0);
            $table->decimal('markup_amount', 10, 2)->default(0);
            $table->string('currency', 3)->default('USD');
            $table->boolean('is_warranty_covered')->default(false);
            $table->date('warranty_start_date')->nullable();
            $table->date('warranty_expiry_date')->nullable();
            $table->integer('warranty_duration_months')->nullable();
            $table->text('warranty_terms')->nullable();
            $table->string('serial_number')->nullable();
            $table->string('batch_number')->nullable();
            $table->enum('part_condition', ['new', 'refurbished', 'used'])->default('new');
            $table->boolean('is_core_part')->default(false); // returnable core
            $table->decimal('core_charge', 10, 2)->nullable();
            $table->boolean('core_returned')->default(false);
            $table->text('installation_notes')->nullable();
            $table->timestamps();

            // Database Constraints
            $table->check('quantity_used > 0');
            $table->check('unit_cost >= 0');
            $table->check('total_cost >= 0');
            $table->check('markup_percentage >= 0');
            $table->check('markup_amount >= 0');

            // Indexes
            $table->index(['service_id']);
            $table->index(['part_number']);
            $table->index(['supplier']);
            $table->index(['warranty_expiry_date']);
            $table->index(['is_warranty_covered']);
            $table->index(['part_condition']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_parts');
    }
};
