<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Vehicle extends Model
{
    protected $fillable = [
        'vehicle_type_id',
        'name',
        'model',
        'plate_number',
        'engine_type',
        'engine_number',
        'registration_date',
        'color',
        'document',
        'notes',
    ];

    public function vehicleType(): BelongsTo
    {
        return $this->belongsTo(VehicleType::class);
    }

    public function services(): HasMany
    {
        return $this->hasMany(Service::class);
    }
}
